using UnityEngine;
using CustomNetworking.Debug;

namespace CustomNetworking.Tests
{
    /// <summary>
    /// 基础测试验证脚本 - 用于快速验证核心功能
    /// 可以在Inspector中手动运行，无需复杂的测试框架
    /// </summary>
    public class BasicTestVerification : MonoBehaviour
    {
        [Header("测试控制")]
        [SerializeField] private bool runOnStart = false;
        [SerializeField] private bool showDetailedLogs = true;

        [Header("测试结果")]
        [SerializeField] private int totalTests = 0;
        [SerializeField] private int passedTests = 0;
        [SerializeField] private int failedTests = 0;

        private void Start()
        {
            if (runOnStart)
            {
                RunBasicTests();
            }
        }

        /// <summary>
        /// 运行基础测试
        /// </summary>
        [ContextMenu("Run Basic Tests")]
        public void RunBasicTests()
        {
            UnityEngine.Debug.Log("=== 开始基础测试验证 ===");

            totalTests = 0;
            passedTests = 0;
            failedTests = 0;

            // 测试1: 单例模式
            TestSingletonPattern();

            // 测试2: UI控制
            TestUIControl();

            // 测试3: 日志功能
            TestLoggingFunctionality();

            // 测试4: 统计信息
            TestDebugStats();

            // 显示结果
            DisplayResults();
        }

        /// <summary>
        /// 测试单例模式
        /// </summary>
        private void TestSingletonPattern()
        {
            try
            {
                var instance1 = NetworkDebugManager.Instance;
                var instance2 = NetworkDebugManager.Instance;

                bool passed = instance1 != null && instance1 == instance2;
                RecordTestResult("SingletonPattern", passed,
                    passed ? "单例模式正常工作" : "单例模式失败");
            }
            catch (System.Exception ex)
            {
                RecordTestResult("SingletonPattern", false, $"异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试UI控制
        /// </summary>
        private void TestUIControl()
        {
            try
            {
                var debugManager = NetworkDebugManager.Instance;

                // 测试显示UI
                debugManager.ShowDebugUI();
                bool showPassed = GetDebugUIState(debugManager);
                RecordTestResult("ShowDebugUI", showPassed,
                    showPassed ? "显示UI成功" : "显示UI失败");

                // 测试隐藏UI
                debugManager.HideDebugUI();
                bool hidePassed = !GetDebugUIState(debugManager);
                RecordTestResult("HideDebugUI", hidePassed,
                    hidePassed ? "隐藏UI成功" : "隐藏UI失败");

                // 测试切换UI
                bool initialState = GetDebugUIState(debugManager);
                debugManager.ToggleDebugUI();
                bool togglePassed = GetDebugUIState(debugManager) != initialState;
                RecordTestResult("ToggleDebugUI", togglePassed,
                    togglePassed ? "切换UI成功" : "切换UI失败");
            }
            catch (System.Exception ex)
            {
                RecordTestResult("UIControl", false, $"异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试日志功能
        /// </summary>
        private void TestLoggingFunctionality()
        {
            try
            {
                var debugManager = NetworkDebugManager.Instance;

                // 清除现有日志
                debugManager.ClearLogs();
                int initialCount = GetLogEntryCount(debugManager);

                // 测试添加日志
                debugManager.LogDebug("测试日志消息", DebugLogType.Info);
                int newCount = GetLogEntryCount(debugManager);

                bool logPassed = newCount > initialCount;
                RecordTestResult("LogDebug", logPassed,
                    logPassed ? $"日志记录成功 ({newCount} 条)" : "日志记录失败");

                // 测试不同类型的日志
                debugManager.LogDebug("警告消息", DebugLogType.Warning);
                debugManager.LogDebug("错误消息", DebugLogType.Error);

                int finalCount = GetLogEntryCount(debugManager);
                bool multipleLogsPassed = finalCount >= newCount + 2;
                RecordTestResult("MultipleLogTypes", multipleLogsPassed,
                    multipleLogsPassed ? $"多类型日志成功 ({finalCount} 条)" : "多类型日志失败");
            }
            catch (System.Exception ex)
            {
                RecordTestResult("LoggingFunctionality", false, $"异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试调试统计
        /// </summary>
        private void TestDebugStats()
        {
            try
            {
                var debugManager = NetworkDebugManager.Instance;
                var stats = debugManager.GetDebugStats();

                bool statsPassed = stats.CurrentFPS >= 0 && stats.TotalLogEntries >= 0;
                RecordTestResult("GetDebugStats", statsPassed,
                    statsPassed ? $"统计信息正常 (FPS: {stats.CurrentFPS:F1}, 日志: {stats.TotalLogEntries})" : "统计信息异常");
            }
            catch (System.Exception ex)
            {
                RecordTestResult("DebugStats", false, $"异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 记录测试结果
        /// </summary>
        private void RecordTestResult(string testName, bool passed, string message)
        {
            totalTests++;

            if (passed)
            {
                passedTests++;
                if (showDetailedLogs)
                    UnityEngine.Debug.Log($"<color=green>[PASS]</color> {testName}: {message}");
            }
            else
            {
                failedTests++;
                if (showDetailedLogs)
                    UnityEngine.Debug.LogError($"<color=red>[FAIL]</color> {testName}: {message}");
            }
        }

        /// <summary>
        /// 显示测试结果
        /// </summary>
        private void DisplayResults()
        {
            float successRate = totalTests > 0 ? (float)passedTests / totalTests * 100f : 0f;

            string resultMessage = $"=== 测试完成 ===\n" +
                                 $"总计: {totalTests}\n" +
                                 $"通过: {passedTests}\n" +
                                 $"失败: {failedTests}\n" +
                                 $"成功率: {successRate:F1}%";

            if (failedTests == 0)
            {
                UnityEngine.Debug.Log($"<color=green>{resultMessage}</color>");
            }
            else
            {
                UnityEngine.Debug.LogWarning($"<color=yellow>{resultMessage}</color>");
            }
        }

        #region 辅助方法

        /// <summary>
        /// 获取调试UI状态
        /// </summary>
        private bool GetDebugUIState(NetworkDebugManager debugManager)
        {
            try
            {
                var field = typeof(NetworkDebugManager).GetField("_showDebugUI",
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                return field != null && (bool)field.GetValue(debugManager);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取日志条目数量
        /// </summary>
        private int GetLogEntryCount(NetworkDebugManager debugManager)
        {
            try
            {
                var field = typeof(NetworkDebugManager).GetField("_logEntries",
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                var logEntries = field?.GetValue(debugManager) as System.Collections.IList;
                return logEntries?.Count ?? 0;
            }
            catch
            {
                return 0;
            }
        }

        #endregion

        /// <summary>
        /// 重置测试环境
        /// </summary>
        [ContextMenu("Reset Test Environment")]
        public void ResetTestEnvironment()
        {
            try
            {
                // 重置单例
                var field = typeof(NetworkDebugManager).GetField("_instance",
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static);
                field?.SetValue(null, null);

                // 清理测试对象
                var existingManagers = FindObjectsOfType<NetworkDebugManager>();
                foreach (var manager in existingManagers)
                {
                    if (Application.isPlaying)
                        Destroy(manager.gameObject);
                    else
                        DestroyImmediate(manager.gameObject);
                }

                UnityEngine.Debug.Log("测试环境已重置");
            }
            catch (System.Exception ex)
            {
                UnityEngine.Debug.LogError($"重置测试环境失败: {ex.Message}");
            }
        }
    }
}
